<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平衡车控制系统学习笔记</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }

        h2 {
            color: #34495e;
            border-left: 4px solid #3498db;
            padding-left: 15px;
            margin-top: 30px;
        }

        h3 {
            color: #2980b9;
            margin-top: 25px;
        }

        h4 {
            color: #27ae60;
            margin-top: 20px;
        }

        ul,
        ol {
            padding-left: 25px;
        }

        li {
            margin-bottom: 8px;
        }

        strong {
            color: #e74c3c;
        }

        code {
            background-color: #f8f9fa;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Consolas', 'Monaco', monospace;
            color: #d63384;
        }

        pre {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            overflow-x: auto;
            margin: 15px 0;
        }

        pre code {
            background: none;
            padding: 0;
            color: #212529;
        }

        .highlight {
            background-color: #fff3cd;
            padding: 10px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }

        .note {
            background-color: #d1ecf1;
            padding: 10px;
            border-left: 4px solid #17a2b8;
            margin: 15px 0;
        }

        .warning {
            background-color: #f8d7da;
            padding: 10px;
            border-left: 4px solid #dc3545;
            margin: 15px 0;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }

        th,
        td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }

        th {
            background-color: #f8f9fa;
            font-weight: bold;
        }

        .mermaid-container {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        .formula {
            text-align: center;
            font-style: italic;
            margin: 15px 0;
            padding: 10px;
            background-color: #e8f4f8;
            border-radius: 5px;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>平衡车控制系统学习笔记</h1>

        <h2>1. 硬件组成</h2>

        <h3>1.1 核心硬件</h3>
        <ul>
            <li><strong>两个电机 + 电机驱动</strong>：提供动力和精确控制</li>
            <li><strong>陀螺仪（IMU）</strong>：测量角度和角速度</li>
            <li><strong>STM32微控制器</strong>：核心控制单元</li>
        </ul>

        <h2>2. 控制理论基础</h2>

        <h3>2.1 基本原理</h3>
        <p>要想小车保持平衡且静止，需要让小车<strong>先平衡后静止</strong>。</p>

        <h3>2.2 双环控制系统</h3>
        <p>控制系统包含两个关键环节：</p>
        <ul>
            <li><strong>直立环（姿态环）</strong>：内环，负责保持小车直立</li>
            <li><strong>速度环</strong>：外环，负制小车运动速度</li>
        </ul>

        <h3>想象一下：用手掌托起一根棍子让它保持竖直</h3>
        <p>这个场景和你问的平衡小车直立控制是完全一样的道理。</p>
        <ul>
            <li><strong>你的目标：</strong> 让棍子保持竖直不倒。</li>
            <li><strong>你的眼睛：</strong> 负责观察棍子的倾斜角度和倾斜的速度。这就是<strong>"反馈"</strong>。</li>
            <li><strong>你的大脑：</strong> 根据眼睛看到的信息，迅速判断手应该往前移动还是往后移动，以及移动多快。这就是<strong>"PID控制器"</strong>。</li>
            <li><strong>你的手：</strong> 执行大脑的指令，实际地移动位置。这就是<strong>"作用对象"</strong>。</li>
        </ul>

        <p>现在，我们把这个比喻套用到平衡小车上，来正式回答你的三个问题。</p>

        <h3>1. 作用对象是谁？</h3>
        <p>在平衡小车的直立环控制中，<strong>作用对象是驱动小车轮子转动的"电机" (Motors)</strong>。</p>

        <div class="note">
            <strong>为什么是电机？</strong> 小车能够保持平衡，不是因为它有什么"魔法"，而是通过快速地移动车轮来实现的。
            <ul>
                <li>当车身向前倒时，电机需要驱动轮子<strong>向前</strong>加速，追上倒下的重心，把它"拉"回来。</li>
                <li>当车身向后倒时，电机需要驱动轮子<strong>向后</strong>加速，同样是为了让车的底座跟上重心的位置。</li>
            </ul>
            所以，整个控制系统最终下达的指令，就是控制电机的<strong>转速和方向</strong>。电机是最终执行"保持平衡"这个动作的物理部件。
        </div>

        <h3>2. 反馈是谁？</h3>
        <p><strong>反馈是"小车当前的倾斜状态"，它由传感器来测量。</strong></p>

        <p>具体来说，这个反馈信息主要包含两个部分：</p>
        <ol>
            <li><strong>倾斜角度 (Angle):</strong> 小车车身相对于"绝对竖直"的地面，到底倾斜了多少度。这是最核心的反馈。</li>
            <li><strong>倾斜角速度 (Angular Velocity):</strong> 小车车身正在以多快的速度倒下或回正。这个信息也非常关键，它能让系统"预判"趋势。</li>
        </ol>

        <p>这些信息通常是由一个叫做<strong>"惯性测量单元 (IMU)"</strong>的传感器模块提供的。IMU上一般集成了：</p>
        <ul>
            <li><strong>加速度计 (Accelerometer):</strong> 在静态或慢速运动时，可以比较准确地测量出角度。</li>
            <li><strong>陀螺仪 (Gyroscope):</strong> 可以非常灵敏地测量出角速度。</li>
        </ul>

        <p>在实际应用中，通常会用算法（比如卡尔曼滤波、互补滤波）将这两个传感器的数据融合起来，得到一个既准确又灵敏的角度和角速度作为最终的反馈值。</p>

        <h3>3. 反馈和作用对象有什么关系？</h3>
        <p><strong>关系非常直接：反馈决定了作用对象的行为，形成了一个"闭环控制"。</strong></p>

        <p>这是一个不断循环、修正的过程，具体流程如下：</p>
        <ol>
            <li><strong>设定目标：</strong> 我们的目标是让小车保持竖直，也就是倾斜角度为 <code>0</code> 度。</li>
            <li><strong>获取反馈：</strong> IMU传感器测量出小车<strong>当前</strong>的倾斜角度（比如向前倾斜了 <code>5</code> 度）。</li>
            <li><strong>计算误差：</strong>
                控制器（单片机里的程序）用"目标角度"减去"当前反馈的角度"，得到一个"误差值"（<code>Error = 0 - 5 = -5</code>）。这个误差告诉我们，离目标还差多远，以及在哪个方向上。
            </li>
            <li><strong>PID控制器进行计算：</strong>
                <ul>
                    <li><strong>P (比例):</strong> 根据当前的误差大小，直接给出一个控制量。误差越大，让电机转得越快。</li>
                    <li><strong>I (积分):</strong> 如果有很小的误差一直存在（比如小车因为重心不完美而一直微微倾斜），积分项会把这些小误差累加起来，慢慢地施加一个力，直到误差被完全消除。
                    </li>
                    <li><strong>D (微分):</strong>
                        根据误差的变化率（也就是角速度）来提供一个控制量。如果小车倒下的速度很快，D项会给出一个很大的反向力来"刹车"，防止它冲过头来回晃动，让系统更稳定。</li>
                </ul>
            </li>
            <li><strong>驱动作用对象：</strong>
                PID算法最终会计算出一个具体的数值，这个数值被转换成PWM信号，去控制<strong>电机驱动板</strong>，从而精确地控制<strong>电机（作用对象）</strong>的转速和方向。</li>
            <li><strong>状态改变：</strong> 电机转动，改变了车轮的位置，这又会反过来影响小车的倾斜角度（反馈）。</li>
            <li><strong>回到第2步</strong>，开始下一次循环。</li>
        </ol>

        <p>这个循环发生得非常快，一秒钟可以进行几百甚至上千次。正是通过这样高频率的"<strong>测量反馈 → 计算误差 → 控制电机</strong>"的闭环过程，小车才能稳定地"站立"起来。</p>

        <p>我们继续用之前的思路来分析。直立环解决了"站稳"的问题，而速度环解决的是"如何按照我们的意愿前进或后退"的问题。</p>

        <h3>速度环核心思想：如何让小车前进？</h3>
        <p>想让小车前进，我们不能直接让轮子往前转。如果你直接给电机一个"向前转"的命令，车身会因为惯性被向后甩，直立环为了不让车倒下，会立刻让轮子往后转来"追"回身体，结果就是小车在原地晃动一下但无法前进。</p>

        <div class="highlight">
            正确的做法是：<strong>让小车"受控地"向前倾斜一个微小的角度</strong>。
        </div>

        <p>直立环会检测到这个前倾，认为"我要向前倒了！"，于是它会立刻驱动电机向前转，试图把车身"扶正"。但因为我们用速度环<strong>持续地</strong>给它一个"向前倾斜"的目标，所以直立环就会持续地驱动电机向前，结果就是小车一边保持着微小的倾斜，一边前进了！
        </p>

        <p>我们想让车走多快，就让它倾斜的角度多大一点。想让车停下来，就让它的目标角度变回0。</p>

        <h3>1. 速度环中的作用对象是谁？</h3>
        <p>这是一个非常关键且容易混淆的点。速度环的直接作用对象<strong>不是电机</strong>。</p>

        <div class="warning">
            速度环的<strong>作用对象是"直立环的目标角度（Setpoint）"</strong>。
        </div>

        <p>换句话说，速度环的计算结果不是一个直接给电机的指令，而是一个<strong>角度值</strong>。它在"指挥"直立环，告诉直立环："嘿，现在不要以绝对竖直（0度）为目标了，请以向前倾斜 <code>2</code>
            度为新的目标来保持平衡。"</p>

        <p>所以，速度环通过修改直立环的目标，间接地控制了小车的运动。</p>

        <h3>2. 速度环中的反馈是谁？</h3>
        <p><strong>反馈是"小车当前的实际速度"</strong>。</p>

        <p>这个速度信息通常来自于安装在电机轴上的<strong>"编码器" (Encoder)</strong>。</p>

        <div class="note">
            <strong>如何得到速度？</strong> 通过读取编码器在单位时间内的脉冲数，我们就能算出电机的转速。再结合车轮的周长，就可以换算出小车在地面上前进或后退的实际线速度。
        </div>

        <p>所以，速度环的反馈就是由编码器测量并计算出的小车当前速度。</p>

        <h3>3. 反馈和作用对象有什么关系？</h3>
        <p><strong>关系是：速度环通过比较"目标速度"和"反馈的实际速度"，来动态调整"直立环的目标倾角"，从而控制小车达到目标速度。</strong></p>

        <p>这个关系形成了一个新的、更大的控制闭环，我们称之为"<strong>外环</strong>"（速度环），它嵌套在"<strong>内环</strong>"（直立环）的外面。这个结构叫做"<strong>串级PID</strong>"。
        </p>

        <p>具体流程如下：</p>
        <ol>
            <li><strong>设定目标：</strong> 你通过遥控器或代码给小车一个"目标速度"（例如，前进 <code>0.5</code> 米/秒）。</li>
            <li><strong>获取反馈：</strong> 电机编码器测量出小车<strong>当前</strong>的实际速度（例如，只有 <code>0.3</code> 米/秒）。</li>
            <li><strong>计算误差：</strong> 速度环控制器计算速度误差
                <code>Speed_Error = 目标速度 - 实际速度</code>（<code>0.5 - 0.3 = 0.2</code>）。误差是正数，说明速度不够。
            </li>
            <li><strong>速度环PID计算：</strong>
                速度环的PID控制器根据这个速度误差，计算出一个"<strong>目标倾角</strong>"。因为当前速度不够，它会计算出一个需要让车身<strong>更向前倾斜</strong>的角度值。</li>
            <li><strong>驱动作用对象：</strong> 这个计算出的"目标倾角"被发送给<strong>直立环</strong>，成为直立环<strong>新的目标</strong>。</li>
            <li><strong>内环（直立环）工作：</strong>
                直立环得到新的目标倾角后，发现当前小车的实际角度和新目标有差距，于是它立刻开始工作，输出PWM信号控制电机转动，让小车在追赶这个新的倾斜角度的过程中，速度也随之提高。</li>
            <li><strong>回到第2步</strong>，当小车速度提高后，下一次循环计算出的速度误差就会变小，速度环输出的目标倾角也会相应减小，最终让小车稳定在目标速度上。</li>
        </ol>

        <div class="mermaid-container">
            <h4>双环控制系统流程图</h4>
            <pre><code>
速度环 - 外环, 控制速度
    用户设定的目标速度 → 速度环PID控制器 ← 反馈: 实际速度
                    ↓
                目标倾角
                    ↓
直立环 - 内环, 控制直立
    目标倾角 → 直立环PID控制器 ← 反馈: 实际角度
                    ↓
                电机控制值PWM
                    ↓
物理系统 - 小车本体
    电机 → 车身与车轮 → IMU传感器 → 实际角度
                    ↓
                编码器 → 实际速度
            </code></pre>
        </div>

        <h2><strong>直立环</strong></h2>
        <p>直立环是用于控制小车的倾斜角度，使小车保持垂直状态。这个环节主要涉及到小车的姿态控制，目的是通过调整电机输出保持小车不倾斜。</p>

        <h3>1. 为什么直立环用PD控制就够了？</h3>
        <p>直立环就像一个杂技演员在走钢丝，他最关心的是对任何晃动做出<strong>瞬间反应</strong>并且<strong>抑制抖动</strong>，而不是确保自己绝对分毫不差地站在钢丝中心。</p>

        <ul>
            <li><strong>P (Proportional - 比例) 的作用：提供恢复力</strong>
                <ul>
                    <li>这是最基本、最重要的部分。当小车倾斜时，P项会提供一个与倾斜角度成正比的力把它拉回来。<strong>倾斜得越多，拉回来的力就越大</strong>。这就像一个弹簧，你把它拉得越开，它回弹的力就越强。没有P，小车根本站不起来。
                    </li>
                </ul>
            </li>
            <li><strong>D (Differential - 微分) 的作用：提供阻尼，抑制振荡</strong>
                <ul>
                    <li>D项关心的是<strong>倾斜的速度</strong>（角速度）。</li>
                    <li>如果小车<strong>正在快速倒下</strong>，D项会提供一个很大的力来"刹住"这个趋势。</li>
                    <li>如果小车在P项的作用下<strong>快速回正</strong>，D项会提供一个反向的力，防止它冲过头（过冲）向另一边倒去。</li>
                    <li>所以，D项就像一个"阻尼器"或"减震器"，它让小车的动作变得<strong>稳定、平顺，不来回晃动</strong>。</li>
                </ul>
            </li>
        </ul>

        <div class="highlight">
            <strong>PD组合起来，就能实现一个反应迅速、动作干脆、稳定不抖的平衡控制。</strong> 对于直立环这个"十万火急"的任务来说，这已经足够了。
        </div>

        <h3>2. 在直立环中使用I项会有什么影响？</h3>
        <p>引入I项（Integral - 积分）会对直立环造成几个致命的问题：</p>

        <h4>1. 积分延迟导致的"过冲"和"振荡"</h4>
        <p>I项的作用是<strong>累积过去的误差</strong>。它的本意是用来消除那些P和D无法消除的微小静态误差（比如小车因为重心偏离而一直歪着0.1度）。</p>

        <p>但在直立环这个动态系统中，这个"记忆力"是有害的。想象一下这个场景：</p>
        <ol>
            <li>你推了小车一下，它向前倾斜。</li>
            <li><strong>P和D立刻反应</strong>，驱动电机向前追，把车身拉回来。</li>
            <li>在车身倾斜的这段时间里，<strong>I项开始默默地累积误差</strong>，这个积分值变得越来越大，它也在产生一个让电机向前的力。</li>
            <li>当车身回到垂直位置时，P和D的作用都变为0了。但是！<strong>I项累积的值不会立刻消失</strong>，它还带着"历史的包袱"，继续命令电机向前转。</li>
            <li>结果，小车被这个滞后的I项推过了头，向后倒去。</li>
            <li>现在车向后倒，I项又开始反向累积误差...</li>
        </ol>

        <p>最终，小车就会像一个喝醉酒的人一样，来回大幅度晃动，难以稳定。I项的滞后性，在直立环这种需要快速响应的场合，会严重破坏系统的稳定性。</p>

        <h4>2. 积分饱和 (Integral Windup)</h4>
        <p>这是一个更严重的问题。如果小车倒地了，或者被拿在手里，它的倾斜角度会很大且长时间保持不变。</p>
        <ul>
            <li>这时，I项会疯狂地累积一个巨大的误差值，直到达到程序设定的上限。这个现象叫做<strong>"积分饱和"</strong>。</li>
            <li>当你把小车扶正，准备让它重新平衡时，这个饱和的、巨大的积分项会瞬间释放，给电机一个极大的指令，导致车轮飞转，小车猛地窜出去，根本无法恢复平衡。</li>
        </ul>

        <h4>3. 与速度环的指令冲突</h4>
        <p>这是最根本的矛盾。我们之前讨论过，速度环是通过<strong>给直立环一个"目标倾角"</strong>来控制小车移动的。</p>
        <ul>
            <li>比如，速度环说："为了前进，请你保持向前倾斜2度"。</li>
            <li>如果直立环里有I项，它的目标永远是让角度误差为0。当它看到这2度的倾角时，它会认为这是一个需要被消除的"静态误差"。</li>
            <li>于是，I项就会不断累积，产生一个向后的力，试图把这2度的倾角"修正"回0度。</li>
        </ul>

        <div class="warning">
            这就导致<strong>直立环的I项在和速度环的命令"打架"</strong>，使得速度控制变得极其困难和不稳定。
        </div>

        <h3>直立环使用的是角速度（gyro）而不是误差的微分来计算D（微分）项</h3>

        <h4>为什么使用角速度而不是误差的微分？</h4>
        <p>这是一个非常专业且关键的问题！能问到这一点，说明你已经深入到了 PID 控制在实际工程应用中的核心。</p>

        <p>理论上，PID 中的 D 项确实是<strong>误差的微分（Derivative of Error）</strong>。但在平衡小车的直立环中，我们几乎总是直接使用陀螺仪（Gyroscope）的输出来作为 D
            项的输入。</p>

        <div class="highlight">
            简单回答就是：<strong>因为直接用陀螺仪数据更干净、更实时、更高效，可以有效避免噪声放大问题。</strong>
        </div>

        <h4>1. 理论与现实的连接</h4>
        <p>首先，我们从数学上证明这两者是等价的。</p>
        <ul>
            <li>PID 的 D 项公式为：<code>D_output = Kd * d(error)/dt</code></li>
            <li>在直立环中，<code>error = Setpoint_Angle - Actual_Angle</code></li>
            <li>我们的目标是保持竖直，所以 <code>Setpoint_Angle = 0</code></li>
            <li>因此，<code>error = 0 - Actual_Angle = -Actual_Angle</code></li>
            <li>代入 D 项公式：<code>D_output = Kd * d(-Actual_Angle)/dt = -Kd * d(Actual_Angle)/dt</code></li>
        </ul>

        <p>现在看关键点：<code>d(Actual_Angle)/dt</code> 是什么？它就是<strong>角度的变化率</strong>，其物理意义就是<strong>角速度（Angular
                Velocity）</strong>。</p>

        <p>而陀螺仪（Gyro）这个传感器，它<strong>直接测量</strong>的物理量就是角速度！</p>

        <div class="formula">
            所以，理论上：d(error)/dt = -(角速度)
        </div>

        <p>这意味着，我们完全可以用陀螺仪测得的角速度值来代替对误差的微分计算。那个负号可以被吸收到 <code>Kd</code> 这个常数里，在调参时调整 <code>Kd</code> 的正负和大小即可。</p>

        <h4>2. 为什么直接用陀螺仪数据是更好的选择？</h4>
        <p>既然理论上等价，为什么在实践中我们"更喜欢"直接用陀螺仪数据，而不是老老实实地对误差（角度）做微分运算呢？</p>

        <h5>关键原因一：避免噪声放大（最重要！）</h5>
        <ul>
            <li><strong>微分的本质：</strong> 在离散的数字系统中，计算微分的常用方法是 <code>(当前值 - 上一次的值) / 时间间隔</code>。对于角度误差，就是
                <code>(error_k - error_{k-1}) / dt</code>。
            </li>
            <li><strong>噪声问题：</strong> 任何传感器数据都有噪声。我们从
                IMU（惯性测量单元）得到的融合角度（<code>Actual_Angle</code>）虽然经过滤波，但仍然存在微小的、高频的抖动。</li>
            <li><strong>微分的致命缺陷：</strong> 当你对一个带有噪声的信号进行微分（做减法）时，这个噪声会被<strong>急剧放大</strong>。想象一下，一个微小的角度跳动，比如从
                <code>0.1</code> 度瞬间跳到 <code>0.15</code> 度，它的差值 <code>0.05</code> 在一个极短的时间间隔内（比如 0.004
                秒）发生，计算出的微分值会变得非常巨大和尖锐。
            </li>
            <li><strong>后果：</strong> 如果用这个充满"毛刺"和"尖峰"的微分值作为 D 项的输入，会导致电机接收到一系列剧烈抖动的指令，小车会发出"嗡嗡"的异响，并且车身会高频颤抖，完全无法稳定。
            </li>
        </ul>

        <div class="note">
            <strong>相比之下，陀螺仪的原始输出虽然也有噪声（主要是零点漂移），但它本身就是一个连续测量的角速度信号，它比"计算出来的微分角度"要平滑得多。</strong>
            直接使用它，就从源头上避免了微分运算带来的噪声放大问题。
        </div>

        <h5>关键原因二：实时性和无延迟</h5>
        <ul>
            <li><code>d(error)/dt</code> 的计算需要"当前"和"上一次"两个时刻的角度值，这本身就引入了一个微小的计算延迟。</li>
            <li>陀螺仪提供的是当前时刻最"新鲜"的角速度物理量。在控制周期极短（几百到几千 Hz）的平衡系统中，任何一点延迟都可能影响控制效果。直接使用陀螺仪数据，实时性最好。</li>
        </ul>

        <h5>关键原因三：效率和概念清晰</h5>
        <ul>
            <li>D 项的物理意义本身就是提供一个与角速度成反比的<strong>阻尼力</strong>，以抑制运动。</li>
            <li>既然陀螺仪已经直接告诉我们角速度了，为什么还要多此一举地先计算角度，再用角度去计算角速度呢？直接使用陀螺仪数据，不仅计算效率更高（少做一次减法和除法），而且概念上更直接——<strong>用物体的角速度来产生阻尼</strong>。
            </li>
        </ul>

        <h4>总结</h4>
        <table>
            <thead>
                <tr>
                    <th>对比项</th>
                    <th><strong>方法一：计算误差的微分</strong> <code>d(error)/dt</code></th>
                    <th><strong>方法二：直接使用陀螺仪</strong> <code>gyro</code></th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><strong>理论基础</strong></td>
                    <td>PID 的原始定义</td>
                    <td>利用 <code>d(error)/dt = -gyro</code> 的等价关系</td>
                </tr>
                <tr>
                    <td><strong>抗噪性</strong></td>
                    <td><strong>差</strong>。会急剧放大角度噪声，导致系统抖动。</td>
                    <td><strong>好</strong>。信号源头更平滑，避免了微分的噪声放大效应。</td>
                </tr>
                <tr>
                    <td><strong>实时性</strong></td>
                    <td>略有延迟（需要两个采样点）</td>
                    <td><strong>高</strong>。直接使用当前时刻的测量值。</td>
                </tr>
                <tr>
                    <td><strong>效率</strong></td>
                    <td>较低（需要减法和除法）</td>
                    <td><strong>高</strong>（直接读取变量）。</td>
                </tr>
                <tr>
                    <td><strong>实践结果</strong></td>
                    <td>小车高频振动，难以稳定，调参困难。</td>
                    <td><strong>小车平稳，响应迅速，易于调参。</strong></td>
                </tr>
            </tbody>
        </table>

        <div class="highlight">
            <strong>结论：</strong> 在平衡小车直立环中，不使用 <code>误差的微分</code> 而是直接使用 <code>陀螺仪角速度</code> 来计算 D
            项，是基于工程实践的、非常明智和必要的优化，其核心目的是为了获得一个<strong>稳定、干净、实时的阻尼信号</strong>。
        </div>

        <pre><code class="language-c">
static float calculate_balance_pd_with_gyro(PID_T * _tpPID, float _current_angle, float _current_gyro)
{
    float error, p_out, d_out, output;

    // 1. 计算误差 (Error)
    // 目标角度 - 当前角度
    error = _tpPID->target - _current_angle;

    // 2. 计算比例项 (P)
    // Kp * 误差
    p_out = _tpPID->kp * error;

    // 3. 计算微分项 (D) - 这是关键！
    // Kd * 角速度 (直接使用角速度，而不是误差的微分)
    d_out = _tpPID->kd * _current_gyro;

    // 4. 计算总输出
    output = p_out + d_out;

    // 5. 对输出进行限幅
    if(output > _tpPID->limit)
        output = _tpPID->limit;
    else if(output < -_tpPID->limit)
        output = -_tpPID->limit;

    return output;
}
        </code></pre>

        <h2>为什么速度环使用的是PI控制器</h2>
        <div class="highlight">
            <strong>线性关系：Ki=(1/200)*Kp，仅调Kp即可。</strong>
        </div>

        <p>速度环（外环）的核心目标是<strong>"准"</strong>，也就是精确地达到并保持我们设定的速度。PI 控制器正是实现这一目标的完美组合。</p>

        <p>简单来说：<strong>P（比例）项让小车能大致地达到目标速度，而 I（积分）项则负责消除所有细微的误差，让小车精确地保持在那个速度上。</strong></p>

        <p>下面我们来详细分析为什么是 PI，以及为什么一般不用 D。</p>

        <h3>P 项的作用：快速响应</h3>
        <ul>
            <li><strong>P 项做了什么？</strong>
                它根据<strong>速度误差</strong>（<code>目标速度 - 实际速度</code>）的大小，来成比例地输出一个<strong>目标倾角</strong>。</li>
            <li><strong>举个例子：</strong> 你想让小车以 <code>1m/s</code> 的速度前进，但它现在是静止的。
                <ol>
                    <li>速度误差很大 (<code>1 - 0 = 1</code>)。</li>
                    <li>P 项会输出一个比较大的目标倾角，比如 <code>3</code> 度。</li>
                    <li>直立环（内环）收到这个 <code>3</code> 度的目标后，会立刻驱动电机让小车前倾并加速。</li>
                    <li>随着小车速度越来越接近 <code>1m/s</code>，速度误差变小，P 项输出的目标倾角也随之减小。</li>
                </ol>
            </li>
            <li><strong>P 项的局限（稳态误差）：</strong> 如果只有 P 项，小车<strong>几乎永远无法精确达到</strong> <code>1m/s</code>。它可能会稳定在
                <code>0.95m/s</code>。为什么？
                <ul>
                    <li>因为小车要维持 <code>0.95m/s</code> 的速度，就必须克服摩擦力等阻力，这意味着它<strong>必须保持一个微小的前倾角度</strong>（比如
                        <code>0.5</code> 度）。
                    </li>
                    <li>而 P 项的输出 = <code>Kp * 速度误差</code>。为了让 P 项输出这 <code>0.5</code> 度的倾角，<code>速度误差</code>就不能为零！</li>
                    <li>所以系统会达到一个平衡：一个微小的速度误差，产生一个足以维持当前速度的倾角。这个无法消除的误差，就叫做<strong>"稳态误差"</strong>。</li>
                </ul>
            </li>
        </ul>

        <h3>I 项的作用：消除稳态误差（核心）</h3>
        <p>I（积分）项就是解决上述"稳态误差"问题的"神器"。</p>

        <ul>
            <li><strong>I 项做了什么？</strong> 它会<strong>持续不断地累积</strong>过去所有的速度误差。</li>
            <li><strong>回到刚才的例子：</strong> 小车稳定在了 <code>0.95m/s</code>，还差 <code>0.05m/s</code> 的速度误差。
                <ol>
                    <li>这个 <code>0.05</code> 虽然小，但一直存在。</li>
                    <li>I 项就开始累积这个误差：<code>0.05, 0.10, 0.15...</code> 它的输出值会随着时间慢慢增大。</li>
                    <li>I 项的输出会叠加到 P 项的输出上，共同构成最终的目标倾角。</li>
                    <li>这个慢慢增大的 I 项，会使小车的目标倾角也慢慢增大（比如从 <code>0.5</code> 度变成 <code>0.55</code> 度，<code>0.6</code> 度...）。
                    </li>
                    <li>小车因此会继续"不情愿地"慢慢加速。</li>
                    <li>这个过程会一直持续，直到小车的实际速度<strong>正好等于</strong> <code>1m/s</code>，此时速度误差变为 0，I 项才停止累积，并保持在当前值。</li>
                </ol>
            </li>
        </ul>

        <div class="note">
            <strong>I 项就像一个有耐心的微调控制器，它的存在保证了只要还有一丁点误差，它就会不断努力，直到误差被完全消除为止。</strong> 这对于需要精确控制速度或位置的"外环"任务至关重要。
        </div>

        <h3>为什么速度环通常不使用 D 项？</h3>
        <p>D（微分）项关心的是<strong>误差的变化率</strong>，它有"预测"和"阻尼"的作用。但在速度环中，使用 D 项通常弊大于利。</p>

        <ol>
            <li><strong>对速度噪声敏感：</strong>
                速度是通过电机编码器计算出来的，这个数据本身可能不是平滑的，而是有微小"台阶"或"抖动"的（噪声）。我们之前讲过，对一个有噪声的信号做微分，会<strong>急剧放大噪声</strong>，导致 D
                项输出剧烈跳动。这会使给内环的目标倾角也跟着跳动，让小车运行起来一顿一顿的，非常"神经质"。</li>
            <li><strong>可能使系统响应变慢：</strong> D 项会抑制误差的快速变化。当你突然改变目标速度时（比如从 0 加速到 1m/s），D
                项会因为误差变化太快而产生一个反向作用力，这可能会减慢系统的加速过程，使响应变得"迟钝"。</li>
            <li><strong>内环已经提供了足够的稳定性：</strong> 真正需要快速响应和抑制振荡的是直立环（内环）。内环的 PD 控制器（尤其是基于陀螺仪的 D
                项）已经为整个系统提供了强大的稳定性和阻尼。外环（速度环）的任务是给内环提供一个平滑、准确的目标，而不是自己也去参与高速的"抖动抑制"。</li>
        </ol>

        <h3>总结：船长与舵手的比喻</h3>
        <p>如果把整个控制系统比作一艘船：</p>
        <ul>
            <li><strong>船长（速度环 - PI）：</strong> 负责制定航行目标。他看着海图说："我们要精确地以 10 节的速度向正北航行！"
                他用<strong>P</strong>来下达大方向指令，用<strong>I</strong>来根据风和水流（类似摩擦力）不断修正航向，确保最终能精确地达到目标。他下达的指令是沉稳而准确的。</li>
            <li><strong>舵手（直立环 - PD）：</strong>
                负责执行船长的指令。他盯着罗盘（船长的目标倾角），用尽全力稳定船身。他用<strong>P</strong>来保持方向，用<strong>D</strong>（他的快速反应和对海浪的预判）来抵消每一个打来的浪花（各种干扰），确保船身始终稳定在船长指定的航向上。他的动作是迅速而敏捷的。
            </li>
        </ul>

        <h2>参数整定及极性判断</h2>

        <h3>1、机械中值</h3>
        <div class="note">
            由于小车结构的问题，小车的重心不一定与0角度在一条直线上，所以我们需要找到让重心平衡的角度，即机械中值。
        </div>

        <p>首先，把小车放在地面上，从前向后以及从后向前绕电机轴旋转平衡小车，两次向另一边倒下的角度的中值，即为机械中值。</p>

        <div class="highlight">
            <strong>举例：往后倒在2度，往后倒在-3度左右，本系统的机械中值为-1度。</strong>
        </div>

        <h3>直立环：</h3>

        <h4>1. Kp极性：</h4>
        <ul>
            <li><strong>极性错误：</strong>小车往哪边倒，车轮就往反方向开，会使得小车加速倒下。</li>
            <li><strong>极性正确：</strong>小车往哪边倒，车轮就往哪边开，以保证小车有直立的趋势。</li>
        </ul>

        <h4>2. Kd极性：</h4>
        <p><strong>如何确定你的 <code>Kd</code> 到底应该是正是负？</strong></p>

        <p>给你一个最简单的实践方法：</p>
        <ol>
            <li><strong>暂时把 <code>Kp</code> 设为0，<code>Kd</code> 设为一个较小的正值。</strong></li>
            <li>用手扶着小车，让它<strong>慢慢向前倒</strong>。</li>
            <li>观察现象：
                <ul>
                    <li><strong>如果车轮向前转，试图阻止你</strong>，说明 <code>Kd</code> 的极性是<strong>正确</strong>的，就用正值。</li>
                    <li><strong>如果车轮向后转，反而加速了倾倒</strong>，说明 <code>Kd</code>
                        的极性是<strong>错误</strong>的，你需要把它改成<strong>负值</strong>。</li>
                </ul>
            </li>
        </ol>

        <div class="warning">
            不要想当然的认为Kp和Kd的符号是一样的(重要)
        </div>

        <h3>速度环</h3>
        <p>先直立环给kp和kd赋值，然后假设速度环kp极性为正进行赋值给一个比较小的值，kp和ki极性相同，如果小车向前倾斜车轮加速转说明极性就是正确，如何极性不同小车在平衡位置，车轮几乎不转，前后倾斜小车转动的也很慢</p>

        <h3>大小怎么调参</h3>
        <p>看视频：</p>
        <p><a href="https://www.bilibili.com/video/BV1zo4y1D7bx/?spm_id_from=333.337.search-card.all.click&vd_source=828c8a90a9ab157df3ce0c0ada60f885"
                target="_blank">平衡车调参教程视频</a></p>

    </div>

    <script>
        // 添加代码高亮
        document.addEventListener('DOMContentLoaded', function () {
            // 为代码块添加语法高亮样式
            const codeBlocks = document.querySelectorAll('pre code');
            codeBlocks.forEach(block => {
                block.style.fontSize = '14px';
                block.style.lineHeight = '1.4';
            });
        });
    </script>
</body>

</html>